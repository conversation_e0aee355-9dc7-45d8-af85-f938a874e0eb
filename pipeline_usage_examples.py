#!/usr/bin/env python3
"""
Usage Examples for Pipeline Integration

This file shows different ways to use the pipeline integration module
in your existing pipeline or applications.
"""

import os
import sys
import json
from typing import List, Dict, Any

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def example_1_simple_usage():
    """Example 1: Simple single plant processing"""
    
    print("📝 Example 1: Simple Usage")
    print("-" * 30)
    
    from pipeline_integration import download_power_plant_pdfs
    
    # Simple one-liner
    result = download_power_plant_pdfs("SEIL Energy")
    
    print(f"Downloaded {result['total_files']} PDFs in {result['processing_time']:.1f} seconds")
    return result

def example_2_custom_directory():
    """Example 2: Custom download directory"""
    
    print("\n📝 Example 2: Custom Directory")
    print("-" * 30)
    
    from pipeline_integration import download_power_plant_pdfs
    
    # Specify custom download directory
    result = download_power_plant_pdfs(
        plant_name="PLTU Suparma",
        download_dir="./my_custom_pdfs"
    )
    
    print(f"PDFs saved to: ./my_custom_pdfs")
    print(f"Files: {result['pdfs_downloaded']}")
    return result

def example_3_batch_processing():
    """Example 3: Process multiple plants"""
    
    print("\n📝 Example 3: Batch Processing")
    print("-" * 30)
    
    from pipeline_integration import download_multiple_power_plants
    
    # List of plants to process
    plants = [
        "SEIL Energy",
        "PLTU Suparma",
        "San Miguel Power"
    ]
    
    # Process all plants
    results = download_multiple_power_plants(plants)
    
    # Print summary
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['plant_name']}: {result['total_files']} PDFs")
    
    return results

def example_4_advanced_pipeline_class():
    """Example 4: Using the pipeline class directly"""
    
    print("\n📝 Example 4: Advanced Pipeline Class")
    print("-" * 30)
    
    from pipeline_integration import PowerPlantPDFPipeline
    
    # Initialize pipeline with custom settings
    pipeline = PowerPlantPDFPipeline(
        download_dir="./advanced_downloads",
        max_search_results=15
    )
    
    # Process step by step
    plant_name = "SEIL Energy"
    
    # Step 1: Search
    urls = pipeline.search_power_plant_urls(plant_name)
    print(f"Found {len(urls)} URLs")
    
    # Step 2: Filter
    relevant_urls = pipeline.analyze_and_filter_urls(urls, plant_name)
    print(f"Filtered to {len(relevant_urls)} relevant URLs")
    
    # Step 3: Download
    downloaded_files = pipeline.download_pdfs_from_urls(relevant_urls, plant_name)
    print(f"Downloaded {len(downloaded_files)} PDFs")
    
    return downloaded_files

def example_5_error_handling():
    """Example 5: Proper error handling"""
    
    print("\n📝 Example 5: Error Handling")
    print("-" * 30)
    
    from pipeline_integration import download_power_plant_pdfs
    
    try:
        # Try to process a plant
        result = download_power_plant_pdfs("NonExistent Power Plant")
        
        if result['success']:
            print(f"✅ Success: {result['total_files']} PDFs downloaded")
        else:
            print(f"❌ Failed: {result['errors']}")
            
        return result
        
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return None

def example_6_integration_with_existing_pipeline():
    """Example 6: Integration with existing data pipeline"""
    
    print("\n📝 Example 6: Pipeline Integration")
    print("-" * 30)
    
    from pipeline_integration import download_power_plant_pdfs
    
    # Simulate existing pipeline data
    power_plants_database = [
        {"id": 1, "name": "SEIL Energy", "country": "India"},
        {"id": 2, "name": "PLTU Suparma", "country": "Indonesia"},
        {"id": 3, "name": "San Miguel Power", "country": "Philippines"}
    ]
    
    # Process each plant in your pipeline
    pipeline_results = []
    
    for plant_data in power_plants_database:
        print(f"Processing plant ID {plant_data['id']}: {plant_data['name']}")
        
        # Download PDFs
        result = download_power_plant_pdfs(
            plant_name=plant_data['name'],
            download_dir=f"./pipeline_data/plant_{plant_data['id']}"
        )
        
        # Add to your pipeline results
        pipeline_result = {
            "plant_id": plant_data['id'],
            "plant_name": plant_data['name'],
            "country": plant_data['country'],
            "pdf_count": result['total_files'],
            "pdf_files": result['pdfs_downloaded'],
            "processing_success": result['success'],
            "processing_time": result['processing_time']
        }
        
        pipeline_results.append(pipeline_result)
        
        # Brief pause between plants
        import time
        time.sleep(1)
    
    # Save pipeline results
    with open("pipeline_results.json", "w") as f:
        json.dump(pipeline_results, f, indent=2)
    
    print(f"Pipeline completed: {len(pipeline_results)} plants processed")
    return pipeline_results

def example_7_filtering_and_validation():
    """Example 7: Custom filtering and validation"""
    
    print("\n📝 Example 7: Custom Filtering")
    print("-" * 30)
    
    from pipeline_integration import download_power_plant_pdfs
    
    # Download PDFs
    result = download_power_plant_pdfs("SEIL Energy")
    
    if result['success']:
        # Custom validation of downloaded files
        valid_files = []
        
        for file_path in result['pdfs_downloaded']:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                
                # Only keep files larger than 100KB
                if file_size > 100 * 1024:
                    valid_files.append(file_path)
                    print(f"✅ Valid: {os.path.basename(file_path)} ({file_size/1024/1024:.1f} MB)")
                else:
                    print(f"❌ Too small: {os.path.basename(file_path)} ({file_size} bytes)")
        
        print(f"Validation: {len(valid_files)}/{len(result['pdfs_downloaded'])} files are valid")
        return valid_files
    
    return []

if __name__ == "__main__":
    print("🚀 Pipeline Integration Usage Examples")
    print("=" * 50)
    
    # Run examples (comment out the ones you don't want to test)
    
    # Basic examples
    example_1_simple_usage()
    example_2_custom_directory()
    
    # Advanced examples (uncomment to test)
    # example_3_batch_processing()
    # example_4_advanced_pipeline_class()
    # example_5_error_handling()
    # example_6_integration_with_existing_pipeline()
    # example_7_filtering_and_validation()
    
    print("\n🎉 Examples completed!")
    print("\n💡 Integration Guide:")
    print("1. Import: from src.pipeline_integration import download_power_plant_pdfs")
    print("2. Use: result = download_power_plant_pdfs('Plant Name')")
    print("3. Check: result['success'] and result['pdfs_downloaded']")
    print("4. Integrate: Add to your existing pipeline workflow")
