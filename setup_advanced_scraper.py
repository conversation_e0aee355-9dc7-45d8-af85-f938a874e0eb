#!/usr/bin/env python3
"""
Setup script for the advanced PDF scraper
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False

def main():
    print("🚀 Setting up Advanced PDF Scraper")
    print("=" * 40)
    
    # Step 1: Install Python dependencies
    if not run_command(
        "pip install -r requirements-advanced-scraper.txt",
        "Installing Python dependencies"
    ):
        print("💡 Try: pip install --upgrade pip")
        print("💡 Or: pip install --user -r requirements-advanced-scraper.txt")
        return False
    
    # Step 2: Install Playwright browsers
    if not run_command(
        "playwright install chromium",
        "Installing Playwright browsers"
    ):
        print("💡 Try running: python -m playwright install chromium")
        return False
    
    # Step 3: Test the installation
    print("\n🔍 Testing installation...")
    
    try:
        # Test imports
        import aiohttp
        import aiofiles
        import playwright
        import tenacity
        print("✅ All Python packages imported successfully")
        
        # Test Playwright
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        print("✅ Playwright browser test successful")
        
        print("\n🎉 Setup completed successfully!")
        print("\n💡 Next steps:")
        print("   1. Run: python test_advanced_scraper.py")
        print("   2. Test with San Miguel: python src/power_plant_search.py")
        print("   3. Enter 'san miguel' when prompted")
        
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed. Please check the error messages above.")
        sys.exit(1)
