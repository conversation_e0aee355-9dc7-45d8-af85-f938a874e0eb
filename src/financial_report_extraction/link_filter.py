"""
Link Filter

Filters and prioritizes financial report links based on various criteria.
This consolidates link filtering logic for better organization and reusability.
"""

from typing import List, Dict, Set
from urllib.parse import urlparse
from collections import defaultdict


class LinkFilter:
    """
    Filters and prioritizes financial report links based on relevance and quality.
    """
    
    def __init__(self):
        # High-priority domains for financial reports
        self.priority_domains = {
            'sec.gov',
            'edgar.sec.gov', 
            'investor.com',
            'ir.com'
        }
        
        # URL patterns that indicate financial/investor relations content
        self.financial_url_patterns = [
            'investor',
            'annual-report',
            'financial',
            'sec-filing',
            '10-k',
            'earnings',
            'quarterly'
        ]
        
        # URL patterns to avoid
        self.negative_url_patterns = [
            'news',
            'press-release',
            'blog',
            'career',
            'contact',
            'privacy',
            'terms'
        ]
    
    def filter_relevant_urls(self, urls: List[str]) -> List[str]:
        """
        Filter URLs to keep only those likely to contain financial reports.
        
        Args:
            urls: List of URLs to filter
            
        Returns:
            Filtered list of relevant URLs
        """
        relevant_urls = []
        
        for url in urls:
            if self._is_relevant_url(url):
                relevant_urls.append(url)
                print(f"   ✅ Relevant: {url}")
            else:
                print(f"   ❌ Filtered out: {url}")
        
        return relevant_urls
    
    def deduplicate_by_domain(self, urls: List[str], max_results: int = 3) -> List[str]:
        """
        Deduplicate URLs by domain, keeping the best URL from each domain.
        
        Args:
            urls: List of URLs to deduplicate
            max_results: Maximum number of URLs to return
            
        Returns:
            Deduplicated list of URLs
        """
        domain_urls = defaultdict(list)
        
        # Group URLs by domain
        for url in urls:
            domain = self._get_domain(url)
            if domain:
                domain_urls[domain].append(url)
        
        # Select best URL from each domain
        unique_urls = []
        for domain, domain_url_list in domain_urls.items():
            best_url = self._select_best_url_for_domain(domain_url_list)
            unique_urls.append(best_url)
            
            if len(unique_urls) >= max_results:
                break
        
        return unique_urls
    
    def prioritize_urls(self, urls: List[str]) -> List[str]:
        """
        Prioritize URLs based on domain authority and URL patterns.
        
        Args:
            urls: List of URLs to prioritize
            
        Returns:
            Prioritized list of URLs
        """
        url_scores = []
        
        for url in urls:
            score = self._calculate_url_score(url)
            url_scores.append((url, score))
        
        # Sort by score (descending)
        url_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [url for url, score in url_scores]
    
    def remove_duplicates(self, urls: List[str]) -> List[str]:
        """
        Remove exact duplicate URLs while preserving order.
        
        Args:
            urls: List of URLs that may contain duplicates
            
        Returns:
            List with duplicates removed
        """
        seen = set()
        unique_urls = []
        
        for url in urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
        
        return unique_urls
    
    def _is_relevant_url(self, url: str) -> bool:
        """Check if a URL is relevant for financial reports."""
        url_lower = url.lower()
        
        # Check for positive indicators
        has_positive = any(pattern in url_lower for pattern in self.financial_url_patterns)
        
        # Check for negative indicators
        has_negative = any(pattern in url_lower for pattern in self.negative_url_patterns)
        
        # URL is relevant if it has positive indicators and no negative ones
        return has_positive and not has_negative
    
    def _get_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except Exception:
            return ""
    
    def _select_best_url_for_domain(self, urls: List[str]) -> str:
        """Select the best URL from a list of URLs from the same domain."""
        if len(urls) == 1:
            return urls[0]
        
        # Score each URL and return the best one
        best_url = urls[0]
        best_score = self._calculate_url_score(best_url)
        
        for url in urls[1:]:
            score = self._calculate_url_score(url)
            if score > best_score:
                best_score = score
                best_url = url
        
        return best_url
    
    def _calculate_url_score(self, url: str) -> float:
        """Calculate a relevance score for a URL."""
        score = 0.0
        url_lower = url.lower()
        
        # Domain priority
        domain = self._get_domain(url)
        if domain in self.priority_domains:
            score += 10.0
        
        # URL pattern scoring
        for pattern in self.financial_url_patterns:
            if pattern in url_lower:
                if pattern in ['annual-report', 'investor', 'financial']:
                    score += 3.0  # High value patterns
                elif pattern in ['sec-filing', '10-k']:
                    score += 5.0  # Very high value patterns
                else:
                    score += 1.0  # Medium value patterns
        
        # Negative pattern penalty
        for pattern in self.negative_url_patterns:
            if pattern in url_lower:
                score -= 2.0
        
        # Shorter, cleaner URLs get slight bonus
        if len(url) < 100:
            score += 0.5
        
        # HTTPS bonus
        if url.startswith('https://'):
            score += 0.5
        
        return max(score, 0.0)  # Ensure non-negative score
    
    def get_domain_distribution(self, urls: List[str]) -> Dict[str, int]:
        """
        Get the distribution of URLs across domains.
        
        Args:
            urls: List of URLs to analyze
            
        Returns:
            Dictionary mapping domains to URL counts
        """
        domain_counts = defaultdict(int)
        
        for url in urls:
            domain = self._get_domain(url)
            if domain:
                domain_counts[domain] += 1
        
        return dict(domain_counts)
    
    def filter_by_domain_limit(self, urls: List[str], max_per_domain: int = 1) -> List[str]:
        """
        Filter URLs to limit the number of URLs per domain.
        
        Args:
            urls: List of URLs to filter
            max_per_domain: Maximum URLs to keep per domain
            
        Returns:
            Filtered list of URLs
        """
        domain_counts = defaultdict(int)
        filtered_urls = []
        
        for url in urls:
            domain = self._get_domain(url)
            if domain and domain_counts[domain] < max_per_domain:
                filtered_urls.append(url)
                domain_counts[domain] += 1
        
        return filtered_urls
