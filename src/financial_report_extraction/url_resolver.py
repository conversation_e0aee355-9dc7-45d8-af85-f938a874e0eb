"""
URL Resolver

Handles URL resolution, redirect following, and URL cleaning for financial report extraction.
This consolidates URL handling logic that was previously scattered across multiple files.
"""

import requests
from typing import Optional
from urllib.parse import urlparse, urljoin


class URLResolver:
    """
    Handles URL resolution and redirect following for financial report links.
    """
    
    def __init__(self, timeout: int = 15, max_redirects: int = 10):
        self.timeout = timeout
        self.max_redirects = max_redirects
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def resolve_redirect_url(self, url: str) -> Optional[str]:
        """
        Resolve a URL by following redirects to get the final destination.
        
        Args:
            url: The URL to resolve
            
        Returns:
            The final resolved URL, or None if resolution fails
        """
        try:
            # Handle Google redirect URLs specifically
            if 'grounding-api-redirect' in url or 'google.com/url' in url:
                return self._resolve_google_redirect(url)
            
            # For other URLs, follow redirects normally
            response = self.session.head(url, allow_redirects=True, timeout=self.timeout)
            
            # Check if we got a valid response
            if response.status_code < 400:
                resolved_url = response.url
                
                # Clean up the resolved URL
                resolved_url = self._clean_url(resolved_url)
                
                return resolved_url
            else:
                print(f"   ⚠️ HTTP {response.status_code} for {url}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed for {url}: {str(e)}")
            return None
        except Exception as e:
            print(f"   ❌ Unexpected error resolving {url}: {str(e)}")
            return None
    
    def _resolve_google_redirect(self, url: str) -> Optional[str]:
        """
        Resolve Google redirect URLs to get the actual destination.
        
        Args:
            url: Google redirect URL
            
        Returns:
            The actual destination URL
        """
        try:
            # Try to extract the actual URL from Google redirect parameters
            if 'url=' in url:
                # Extract URL parameter
                import urllib.parse
                parsed = urllib.parse.urlparse(url)
                params = urllib.parse.parse_qs(parsed.query)
                
                if 'url' in params:
                    actual_url = params['url'][0]
                    # Decode URL if needed
                    actual_url = urllib.parse.unquote(actual_url)
                    return self._clean_url(actual_url)
            
            # If parameter extraction fails, try following the redirect
            response = self.session.get(url, allow_redirects=True, timeout=self.timeout)
            if response.status_code < 400:
                return self._clean_url(response.url)
            
            return None
            
        except Exception as e:
            print(f"   ❌ Failed to resolve Google redirect {url}: {str(e)}")
            return None
    
    def _clean_url(self, url: str) -> str:
        """
        Clean and normalize a URL.
        
        Args:
            url: URL to clean
            
        Returns:
            Cleaned URL
        """
        # Remove common tracking parameters
        tracking_params = [
            'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
            'fbclid', 'gclid', 'ref', 'source', 'campaign'
        ]
        
        try:
            from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
            
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            
            # Remove tracking parameters
            cleaned_params = {k: v for k, v in query_params.items() 
                            if k.lower() not in tracking_params}
            
            # Rebuild query string
            new_query = urlencode(cleaned_params, doseq=True)
            
            # Rebuild URL
            cleaned_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path,
                parsed.params,
                new_query,
                parsed.fragment
            ))
            
            return cleaned_url
            
        except Exception:
            # If cleaning fails, return original URL
            return url
    
    def is_valid_url(self, url: str) -> bool:
        """
        Check if a URL is valid and accessible.
        
        Args:
            url: URL to validate
            
        Returns:
            True if URL is valid and accessible
        """
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Try a HEAD request to check if URL is accessible
            response = self.session.head(url, timeout=self.timeout)
            return response.status_code < 400
            
        except Exception:
            return False
    
    def get_domain(self, url: str) -> Optional[str]:
        """
        Extract domain from URL.
        
        Args:
            url: URL to extract domain from
            
        Returns:
            Domain name or None if extraction fails
        """
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except Exception:
            return None
    
    def is_same_domain(self, url1: str, url2: str) -> bool:
        """
        Check if two URLs are from the same domain.
        
        Args:
            url1: First URL
            url2: Second URL
            
        Returns:
            True if both URLs are from the same domain
        """
        domain1 = self.get_domain(url1)
        domain2 = self.get_domain(url2)
        
        if not domain1 or not domain2:
            return False
        
        return domain1 == domain2
    
    def make_absolute_url(self, base_url: str, relative_url: str) -> str:
        """
        Convert a relative URL to an absolute URL.
        
        Args:
            base_url: Base URL to resolve against
            relative_url: Relative URL to convert
            
        Returns:
            Absolute URL
        """
        return urljoin(base_url, relative_url)
    
    def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'session'):
            self.session.close()
