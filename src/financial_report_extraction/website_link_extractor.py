"""
Website Link Extractor

Main class responsible for extracting financial and annual report website links
from power plant search results. This consolidates the logic that was previously
spread across multiple files.
"""

import re
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, urljoin

from .content_analyzer import ContentAnalyzer
from .url_resolver import URLResolver
from .link_filter import LinkFilter


class WebsiteLinkExtractor:
    """
    Extracts and processes financial/annual report website links from search results.
    
    This class handles the complete pipeline from raw search results to filtered,
    prioritized website links suitable for PDF scraping.
    """
    
    def __init__(self):
        self.content_analyzer = ContentAnalyzer()
        self.url_resolver = URLResolver()
        self.link_filter = LinkFilter()
    
    def extract_links_from_search_results(self, final_state: Dict, max_results: int = 3) -> Tuple[List[str], List[str]]:
        """
        Extract financial report website links from search results.
        
        Args:
            final_state: The final state from the search agent containing messages and sources
            max_results: Maximum number of links to return
            
        Returns:
            Tuple of (prioritized_urls, all_found_urls)
        """
        print("\n🔗 Extracting financial report website links...")
        
        # Extract URLs from AI answer (highest priority)
        answer_urls = self._extract_urls_from_answer(final_state)
        
        # Extract URLs from search sources (backup)
        source_urls = self._extract_urls_from_sources(final_state)
        
        # Combine and prioritize URLs
        prioritized_urls = self._prioritize_urls(answer_urls, source_urls)
        
        # Resolve redirects and clean URLs
        resolved_urls = self._resolve_and_clean_urls(prioritized_urls)
        
        # Filter and rank URLs based on content analysis
        filtered_urls = self._filter_and_rank_urls(resolved_urls, max_results)
        
        return filtered_urls, resolved_urls
    
    def _extract_urls_from_answer(self, final_state: Dict) -> List[str]:
        """Extract URLs from the AI-generated answer."""
        answer_urls = []
        
        if final_state and final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                
                # Extract URLs using regex
                found_urls = re.findall(r'https?://[^\s\)\]\*]+', answer)
                
                # Clean up URLs
                clean_urls = []
                for url in found_urls:
                    # Remove trailing punctuation and markdown
                    url = url.rstrip('.,;:!?)*')
                    url = url.replace('**', '')
                    if url.startswith('http') and 'grounding-api-redirect' not in url:
                        clean_urls.append(url)
                        print(f"   🎯 AI-identified URL: {url}")
                
                answer_urls = clean_urls
                
                if answer_urls:
                    print(f"   ✅ Found {len(answer_urls)} AI-identified URLs")
                else:
                    print(f"   ⚠️  No direct URLs found in AI answer")
        
        return answer_urls
    
    def _extract_urls_from_sources(self, final_state: Dict) -> List[str]:
        """Extract URLs from search sources."""
        source_urls = []
        
        if final_state and final_state.get("sources"):
            sources = final_state["sources"]
            print(f"\n📊 Processing {len(sources)} search sources...")
            
            for i, source in enumerate(sources):
                if isinstance(source, dict) and 'url' in source:
                    url = source['url']
                    source_urls.append(url)
                    print(f"   📎 Source {i+1}: {url}")
        
        return source_urls
    
    def _prioritize_urls(self, answer_urls: List[str], source_urls: List[str]) -> List[str]:
        """Prioritize AI-identified URLs over source URLs."""
        if answer_urls:
            # Use AI-identified URLs first, then add source URLs as backup
            all_urls = answer_urls + [url for url in source_urls if url not in answer_urls]
            print(f"📊 Prioritizing {len(answer_urls)} AI URLs, with {len(source_urls)} source URLs as backup")
        else:
            # Fallback to source URLs if no AI URLs found
            all_urls = source_urls
            print(f"📊 Using {len(source_urls)} source URLs (no AI URLs found)")
        
        return all_urls
    
    def _resolve_and_clean_urls(self, urls: List[str]) -> List[str]:
        """Resolve redirects and clean URLs."""
        print(f"\n🔄 Resolving {len(urls)} URLs...")
        resolved_urls = []
        
        for url in urls:
            try:
                resolved_url = self.url_resolver.resolve_redirect_url(url)
                if resolved_url and resolved_url not in resolved_urls:
                    resolved_urls.append(resolved_url)
                    if resolved_url != url:
                        print(f"   🔄 {url[:50]}... → {resolved_url[:50]}...")
                    else:
                        print(f"   ✅ {resolved_url[:70]}...")
            except Exception as e:
                print(f"   ❌ Failed to resolve {url[:50]}...: {str(e)}")
                continue
        
        print(f"✅ Successfully resolved {len(resolved_urls)} URLs")
        return resolved_urls
    
    def _filter_and_rank_urls(self, urls: List[str], max_results: int) -> List[str]:
        """Filter and rank URLs based on content analysis."""
        print(f"\n🧠 Analyzing content of {len(urls)} URLs...")
        
        # Use content analyzer to find the best annual report URLs
        best_urls = self.content_analyzer.get_best_annual_report_urls(urls, max_results=max_results)
        
        if not best_urls:
            print("⚠️ No relevant annual report content found, using domain deduplication as fallback")
            # Deduplicate by domain as fallback
            unique_urls = self.link_filter.deduplicate_by_domain(urls, max_results)
        else:
            unique_urls = best_urls
            print(f"✅ Selected {len(unique_urls)} best URLs based on content analysis")
        
        return unique_urls
    
    def generate_alternative_urls(self, base_urls: List[str], company_patterns: Dict[str, List[str]] = None) -> List[str]:
        """
        Generate alternative URL patterns for common annual report locations.
        
        Args:
            base_urls: List of base URLs to generate alternatives for
            company_patterns: Optional dict of company-specific URL patterns
            
        Returns:
            List of alternative URLs to try
        """
        alternative_urls = []
        
        # Default company patterns
        if company_patterns is None:
            company_patterns = {
                'sanmiguel.com.ph': [
                    "/investor-relations/annual-reports",
                    "/corporate/investor-relations/annual-reports",
                    "/investors/annual-reports"
                ],
                'smc.com.ph': [
                    "/investor-relations/annual-reports/",
                    "/investors/financial-reports/"
                ]
            }
        
        for url in base_urls:
            domain = urlparse(url).netloc.lower()
            
            # Check for company-specific patterns
            for company_domain, patterns in company_patterns.items():
                if company_domain in domain:
                    base_url = f"https://{domain}"
                    for pattern in patterns:
                        alternative_url = urljoin(base_url, pattern)
                        if alternative_url not in alternative_urls:
                            alternative_urls.append(alternative_url)
            
            # Generic patterns for any domain
            if not any(company_domain in domain for company_domain in company_patterns.keys()):
                base_url = f"https://{domain}"
                generic_patterns = [
                    "/investor-relations/annual-reports",
                    "/investors/annual-reports", 
                    "/investors/financial-reports",
                    "/corporate/investor-relations",
                    "/about/investor-relations"
                ]
                
                for pattern in generic_patterns:
                    alternative_url = urljoin(base_url, pattern)
                    if alternative_url not in alternative_urls:
                        alternative_urls.append(alternative_url)
        
        return alternative_urls
