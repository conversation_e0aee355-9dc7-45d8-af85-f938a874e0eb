#!/usr/bin/env python3
"""
Test script for the integrated advanced PDF scraper
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_san_miguel_advanced():
    """Test the San Miguel website with advanced scraping"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if SCRAPER_API_KEY is set
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        return
    
    print("🔧 Testing San Miguel with integrated advanced scraper...")
    
    from agent.scraper_api_pdf_scraper import Scrap<PERSON><PERSON><PERSON><PERSON>Scraper
    
    # Initialize the scraper
    scraper = ScraperAPIPDFScraper(download_dir="./annual_reports")
    
    # Test URLs that are known to be problematic
    test_urls = [
        "https://www.sanmiguel.com.ph/corporate/investor-relations/financial-performance/annual-reports"
    ]
    plant_name = "san_miguel"
    
    print(f"\n🔍 Testing integrated scraper with: {test_urls[0]}")
    
    try:
        # This will first try the standard methods, then fallback to advanced
        downloaded_files = scraper.scrape_annual_reports(test_urls, plant_name)
        
        if downloaded_files:
            print(f"\n✅ Success! Downloaded {len(downloaded_files)} files:")
            for file_path in downloaded_files:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   📄 {os.path.basename(file_path)} ({file_size/1024/1024:.2f} MB)")
                else:
                    print(f"   ❌ {file_path} (file not found)")
            return True
        else:
            print("\n❌ No files downloaded")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        ('aiohttp', 'aiohttp'),
        ('aiofiles', 'aiofiles'), 
        ('playwright', 'playwright'),
        ('tenacity', 'tenacity')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} - installed")
        except ImportError:
            print(f"❌ {package_name} - missing")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements-advanced-scraper.txt")
        print("Then run: playwright install chromium")
        return False
    else:
        print("\n✅ All dependencies are installed!")
        return True

def install_playwright_browsers():
    """Install Playwright browsers"""
    try:
        import subprocess
        print("🔧 Installing Playwright browsers...")
        result = subprocess.run(['playwright', 'install', 'chromium'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Playwright browsers installed successfully")
            return True
        else:
            print(f"❌ Failed to install browsers: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing browsers: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Advanced PDF Scraper Integration")
    print("=" * 50)
    
    # Step 1: Check dependencies
    if not test_dependencies():
        print("\n💡 To install missing dependencies:")
        print("   pip install -r requirements-advanced-scraper.txt")
        print("   playwright install chromium")
        sys.exit(1)
    
    # Step 2: Install browsers if needed
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        print("✅ Playwright browsers are ready")
    except Exception as e:
        print("⚠️ Playwright browsers need installation")
        if not install_playwright_browsers():
            sys.exit(1)
    
    # Step 3: Test the integration
    print("\n" + "=" * 50)
    success = test_san_miguel_advanced()
    
    if success:
        print("\n🎉 Integration test passed! Advanced scraper is working.")
        print("💡 The system will now automatically fallback to advanced scraping")
        print("   when standard methods find 0 PDFs.")
    else:
        print("\n❌ Integration test failed.")
        print("💡 Check the error messages above for troubleshooting.")
