#!/usr/bin/env python3
"""
Test script for the new content-based URL analyzer
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.content_analyzer import ContentAnalyzer


def test_content_analyzer():
    """Test the content analyzer with various URL types."""
    print("🧠 Testing Content-Based URL Analyzer")
    print("=" * 50)
    
    # Test URLs that demonstrate the problem
    test_urls = [
        # Clean URLs (should be detected)
        "https://www.aep.com/investors/financial/",
        "https://www.smc.com.ph/investor-relations/annual-reports/",
        "https://www.sanmiguel.com.ph/investor-relations/annual-reports",
        
        # Obfuscated URLs (would be missed by name-only filtering)
        "https://www.aep.com/investors/uihrwebf0972b23948u20",  # Example obfuscated URL
        "https://www.example-power.com/corporate/xyz123abc",     # Another obfuscated example
        
        # Non-relevant URLs (should be rejected)
        "https://www.aep.com/about/careers",
        "https://www.aep.com/news/press-releases",
        "https://www.google.com",
    ]
    
    print(f"🔍 Testing {len(test_urls)} URLs...")
    print("💡 This demonstrates how content analysis finds relevant pages")
    print("   even when URLs are obfuscated or don't contain obvious keywords")
    
    analyzer = ContentAnalyzer()
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{i}. Testing: {url}")
        
        # Analyze the URL content
        result = analyzer.analyze_url_content(url, use_scraper_api=False)
        
        # Show results
        if result["relevant"]:
            print(f"   ✅ RELEVANT (score: {result.get('score', 0)}, confidence: {result.get('confidence', 'unknown')})")
            if result.get("pdf_count", 0) > 0:
                print(f"   📄 Found {result['pdf_count']} PDF links")
        else:
            print(f"   ❌ Not relevant - {result.get('reason', 'Unknown reason')}")
    
    print(f"\n" + "="*50)
    print("🎯 CONTENT ANALYZER BENEFITS:")
    print("✅ Analyzes actual webpage content, not just URL names")
    print("✅ Finds obfuscated URLs like /investors/uihrwebf0972b23948u20")
    print("✅ Rejects irrelevant pages even if they're from the same domain")
    print("✅ Uses ScraperAPI for JavaScript-heavy sites")
    print("✅ Scores pages by relevance to find the best ones")


def test_best_url_selection():
    """Test the best URL selection feature."""
    print(f"\n🏆 Testing Best URL Selection")
    print("=" * 40)
    
    # Mix of good and bad URLs
    mixed_urls = [
        "https://www.aep.com/about/careers",                    # Irrelevant
        "https://www.aep.com/investors/financial/",             # Relevant
        "https://www.aep.com/news/press-releases",              # Irrelevant  
        "https://www.smc.com.ph/investor-relations/annual-reports/",  # Very relevant
        "https://www.google.com",                               # Irrelevant
    ]
    
    analyzer = ContentAnalyzer()
    best_urls = analyzer.get_best_annual_report_urls(mixed_urls, max_results=2)
    
    print(f"\n📊 RESULTS:")
    print(f"   Input: {len(mixed_urls)} URLs")
    print(f"   Output: {len(best_urls)} best URLs")
    
    for i, url in enumerate(best_urls, 1):
        print(f"   {i}. {url}")
    
    print(f"\n💡 This shows how the system automatically filters out")
    print(f"   irrelevant URLs and returns only the best ones!")


def test_san_miguel_example():
    """Test with the actual San Miguel URLs that were problematic."""
    print(f"\n🏭 Testing San Miguel Example")
    print("=" * 35)
    
    # These are the types of URLs we get from the search
    san_miguel_urls = [
        "https://www.sanmiguel.com.ph/corporate/investor-relations/financial-performance/financial-statements",
        "https://www.sanmiguel.com.ph/our-story/our-businesses/power",
        "https://www.sanmiguel.com.ph/investor-relations/annual-reports",  # This is the correct one
    ]
    
    print("🔍 These are the URLs we typically get from search results:")
    for i, url in enumerate(san_miguel_urls, 1):
        print(f"   {i}. {url}")
    
    print(f"\n🧠 Analyzing content to find the best annual report page...")
    
    analyzer = ContentAnalyzer()
    best_urls = analyzer.get_best_annual_report_urls(san_miguel_urls, max_results=1)
    
    if best_urls:
        print(f"\n🎯 BEST URL IDENTIFIED:")
        print(f"   {best_urls[0]}")
        print(f"\n✅ This should be the correct annual reports page!")
    else:
        print(f"\n❌ No relevant URLs found")


if __name__ == "__main__":
    # Check for API key
    if not os.getenv("SCRAPER_API_KEY"):
        print("⚠️ Note: SCRAPER_API_KEY not set. Will use direct HTTP requests only.")
        print("   Set SCRAPER_API_KEY for JavaScript-heavy site support.")
        print()
    
    test_content_analyzer()
    test_best_url_selection()
    test_san_miguel_example()
    
    print(f"\n" + "="*60)
    print("🚀 READY TO TEST WITH REAL POWER PLANT SEARCHES!")
    print("   The system now analyzes actual webpage content")
    print("   instead of just relying on URL patterns.")
    print("="*60)
