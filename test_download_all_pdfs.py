#!/usr/bin/env python3
"""
Test script to verify that ALL PDFs are now downloaded (not just filtered ones)
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_download_all_pdfs():
    """Test that all PDFs are downloaded from PLTU Suparma"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if SCRAPER_API_KEY is set
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        return
    
    print("🔧 Testing Download ALL PDFs (no filtering)...")
    
    from agent.scraper_api_pdf_scraper import Scraper<PERSON><PERSON>DFScraper
    
    # Initialize the scraper
    scraper = Scraper<PERSON><PERSON>DFScraper(download_dir="./annual_reports")
    
    # Test URL
    test_url = "https://www.ptsuparmatbk.com/annual-reports"
    plant_name = "PLTU_Suparma"
    
    print(f"\n🔍 Testing PDF discovery from: {test_url}")
    print("Expected behavior: Accept ALL PDF links (no filtering)")
    
    try:
        # Find PDF links on the page
        pdf_links = scraper.find_pdf_links(test_url)
        
        print(f"\n📊 Results:")
        print(f"   Total PDF links found: {len(pdf_links)}")
        
        if pdf_links:
            print(f"\n📄 All PDF links that will be downloaded:")
            for i, pdf_info in enumerate(pdf_links, 1):
                filename = pdf_info.get('filename', 'unknown.pdf')
                url = pdf_info.get('url', '')
                print(f"   {i:2d}. {filename}")
                print(f"       URL: {url}")
            
            # Ask user if they want to download all
            print(f"\n🤔 Found {len(pdf_links)} PDFs. Download all? (y/n): ", end="")
            response = input().strip().lower()
            
            if response == 'y':
                print(f"\n⬇️  Downloading all {len(pdf_links)} PDFs...")
                
                downloaded_files = []
                for i, pdf_info in enumerate(pdf_links, 1):
                    print(f"\n📄 Downloading {i}/{len(pdf_links)}: {pdf_info.get('filename', 'unknown.pdf')}")
                    
                    file_path = scraper.download_pdf_smart(pdf_info["url"], plant_name)
                    if file_path:
                        downloaded_files.append(file_path)
                        print(f"✅ Success: {os.path.basename(file_path)}")
                    else:
                        print(f"❌ Failed: {pdf_info.get('filename', 'unknown.pdf')}")
                
                print(f"\n📊 Download Summary:")
                print(f"   Successfully downloaded: {len(downloaded_files)}/{len(pdf_links)} files")
                
                if downloaded_files:
                    print(f"\n📁 Downloaded files:")
                    total_size = 0
                    for file_path in downloaded_files:
                        if os.path.exists(file_path):
                            file_size = os.path.getsize(file_path)
                            total_size += file_size
                            print(f"   📄 {os.path.basename(file_path)} ({file_size/1024/1024:.2f} MB)")
                        else:
                            print(f"   ❌ {file_path} (file not found)")
                    
                    print(f"\n💾 Total downloaded: {total_size/1024/1024:.2f} MB")
                    print(f"📁 Files saved in: ./annual_reports/{plant_name}/")
                
                return len(downloaded_files) > 0
            else:
                print("⏭️  Skipping download.")
                return True
        else:
            print("❌ No PDF links found")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Download ALL PDFs (No Filtering)")
    print("=" * 50)
    
    success = test_download_all_pdfs()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("💡 The system now downloads ALL PDFs from the site.")
        print("💡 You can manually check which ones are relevant annual reports.")
    else:
        print("\n❌ Test failed.")
        print("💡 Check the error messages above for troubleshooting.")
