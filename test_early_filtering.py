#!/usr/bin/env python3
"""
Test script to verify early filtering is working at the web research stage
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_early_filtering():
    """Test that early filtering prevents accumulation of irrelevant sources."""
    print("🔬 Testing Early Filtering at Web Research Stage")
    print("=" * 60)
    print("🎯 Goal: Filter sources immediately during web research")
    print("🎯 Expected: Only 1 relevant source should accumulate")
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for SEIL
        initial_state = initialize_power_plant_state("SEIL")
        
        # Run the search
        print("\n🔍 Running search with early filtering...")
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze results
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 EARLY FILTERING RESULTS:")
        print(f"   Total sources accumulated: {len(sources)}")
        print(f"   Target: 1 source")
        
        if len(sources) == 1:
            print(f"   🎯 PERFECT! Early filtering worked - only 1 source")
        elif len(sources) <= 3:
            print(f"   ✅ GOOD! Early filtering mostly worked - {len(sources)} sources")
        else:
            print(f"   ❌ FAILED! Early filtering didn't work - {len(sources)} sources")
        
        # Analyze source quality
        print(f"\n🔍 SOURCE ANALYSIS:")
        
        # Check for duplicates
        labels = [s.get('label', '') for s in sources]
        unique_labels = set(labels)
        
        print(f"   Unique labels: {len(unique_labels)}")
        print(f"   Duplicate labels: {len(labels) - len(unique_labels)}")
        
        if len(unique_labels) == len(labels):
            print("   ✅ No duplicate labels")
        else:
            print("   ❌ Found duplicate labels!")
        
        # Show each source
        for i, source in enumerate(sources, 1):
            label = source.get('label', 'Unknown')
            print(f"\n   {i}. Label: {label}")
            
            # Check if it's SEIL related
            if 'seil' in label.lower():
                print(f"      ✅ SEIL-related source")
            else:
                print(f"      ⚠️  Not SEIL-related: {label}")
            
            # Check if it's a third-party site
            third_party_sites = ['tracxn', 'zaubacorp', 'careratings', 'pitchbook', 
                               'crisilratings', 'icra', 'crisil', 'ptsuparmatbk']
            
            if any(site in label.lower() for site in third_party_sites):
                print(f"      ❌ THIRD-PARTY SITE (should be filtered!)")
            else:
                print(f"      ✅ Not a third-party site")
        
        # Check final answer
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER CHECK:")
                
                # Check for SEIL Energy
                if "seilenergy.com" in answer.lower():
                    print("   ✅ Contains SEIL Energy website")
                else:
                    print("   ❌ Missing SEIL Energy website")
                
                # Check for the correct URL
                if "investorrelations/annualreportinv" in answer.lower():
                    print("   🎯 PERFECT! Contains exact correct URL")
                elif "annual" in answer.lower() and "report" in answer.lower():
                    print("   ✅ Contains annual report reference")
                else:
                    print("   ❌ No annual report reference found")
        
        # Overall assessment
        print(f"\n🏆 OVERALL ASSESSMENT:")
        
        seil_sources = sum(1 for s in sources if 'seil' in s.get('label', '').lower())
        third_party_sources = sum(1 for s in sources 
                                if any(site in s.get('label', '').lower() 
                                     for site in ['tracxn', 'careratings', 'icra', 'ptsuparmatbk']))
        
        print(f"   SEIL sources: {seil_sources}")
        print(f"   Third-party sources: {third_party_sources}")
        print(f"   Total sources: {len(sources)}")
        
        if len(sources) == 1 and seil_sources == 1 and third_party_sources == 0:
            print("   🏆 EXCELLENT! Perfect early filtering")
            print("   💡 System is working optimally")
        elif len(sources) <= 3 and third_party_sources == 0:
            print("   ✅ VERY GOOD! Clean filtering, minimal sources")
        elif third_party_sources == 0:
            print("   ✅ GOOD! No third-party sources")
        else:
            print("   ❌ NEEDS WORK! Still has issues")
            print("   💡 Early filtering may not be working properly")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


if __name__ == "__main__":
    test_early_filtering()
