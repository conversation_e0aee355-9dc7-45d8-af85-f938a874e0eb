"""
Test script for the Financial Report Extraction Module

This script tests the functionality of the new financial report extraction module
to ensure it works correctly and can be integrated into the existing system.
"""

import os
import sys

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_module_imports():
    """Test that all modules can be imported correctly."""
    print("🧪 Testing module imports...")
    
    try:
        from financial_report_extraction import (
            WebsiteLinkExtractor, 
            ContentAnalyzer, 
            URLResolver, 
            LinkFilter
        )
        print("   ✅ All modules imported successfully")
        return True
    except ImportError as e:
        print(f"   ❌ Import error: {str(e)}")
        return False


def test_website_link_extractor():
    """Test the main WebsiteLinkExtractor class."""
    print("\n🧪 Testing WebsiteLinkExtractor...")
    
    try:
        from financial_report_extraction import WebsiteLinkExtractor
        
        # Initialize extractor
        extractor = WebsiteLinkExtractor()
        print("   ✅ WebsiteLinkExtractor initialized")
        
        # Test with sample search results
        sample_results = {
            "messages": [
                {
                    "content": "Found annual reports at https://www.sanmiguel.com.ph/investor-relations/ and https://www.smc.com.ph/annual-reports/"
                }
            ],
            "sources": [
                {"url": "https://example.com/investor-relations"},
                {"url": "https://example.com/financial-reports"}
            ]
        }
        
        # Test URL extraction from answer
        answer_urls = extractor._extract_urls_from_answer(sample_results)
        print(f"   ✅ Extracted {len(answer_urls)} URLs from answer")
        
        # Test URL extraction from sources
        source_urls = extractor._extract_urls_from_sources(sample_results)
        print(f"   ✅ Extracted {len(source_urls)} URLs from sources")
        
        # Test URL prioritization
        prioritized = extractor._prioritize_urls(answer_urls, source_urls)
        print(f"   ✅ Prioritized {len(prioritized)} URLs")
        
        # Test alternative URL generation
        alternatives = extractor.generate_alternative_urls(["https://www.sanmiguel.com.ph/"])
        print(f"   ✅ Generated {len(alternatives)} alternative URLs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ WebsiteLinkExtractor test failed: {str(e)}")
        return False


def test_content_analyzer():
    """Test the ContentAnalyzer class."""
    print("\n🧪 Testing ContentAnalyzer...")
    
    try:
        from financial_report_extraction import ContentAnalyzer
        
        # Initialize analyzer
        analyzer = ContentAnalyzer()
        print("   ✅ ContentAnalyzer initialized")
        
        # Test content analysis (without making actual HTTP requests)
        from bs4 import BeautifulSoup
        
        # Create sample HTML content
        sample_html = """
        <html>
            <head><title>Annual Reports - Investor Relations</title></head>
            <body>
                <h1>Annual Reports</h1>
                <p>Download our latest annual report and financial statements.</p>
                <a href="/reports/annual-report-2023.pdf">2023 Annual Report</a>
                <a href="/reports/10k-2023.pdf">Form 10-K 2023</a>
            </body>
        </html>
        """
        
        soup = BeautifulSoup(sample_html, 'html.parser')
        analysis = analyzer._analyze_content(soup, "https://example.com/investor-relations")
        
        print(f"   ✅ Content analysis completed - Relevant: {analysis['relevant']}, Score: {analysis['score']}")
        
        # Test PDF link finding
        pdf_links = analyzer._find_pdf_links(soup, "https://example.com/investor-relations")
        print(f"   ✅ Found {len(pdf_links)} PDF links")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ContentAnalyzer test failed: {str(e)}")
        return False


def test_url_resolver():
    """Test the URLResolver class."""
    print("\n🧪 Testing URLResolver...")
    
    try:
        from financial_report_extraction import URLResolver
        
        # Initialize resolver
        resolver = URLResolver()
        print("   ✅ URLResolver initialized")
        
        # Test URL cleaning
        dirty_url = "https://example.com/page?utm_source=google&utm_campaign=test&ref=social"
        clean_url = resolver._clean_url(dirty_url)
        print(f"   ✅ URL cleaning: {len(dirty_url)} → {len(clean_url)} chars")
        
        # Test domain extraction
        domain = resolver.get_domain("https://www.sanmiguel.com.ph/investor-relations")
        print(f"   ✅ Domain extraction: {domain}")
        
        # Test domain comparison
        same_domain = resolver.is_same_domain(
            "https://www.example.com/page1",
            "https://www.example.com/page2"
        )
        print(f"   ✅ Domain comparison: {same_domain}")
        
        # Test absolute URL creation
        absolute = resolver.make_absolute_url(
            "https://example.com/base/",
            "../reports/annual.pdf"
        )
        print(f"   ✅ Absolute URL: {absolute}")
        
        # Cleanup
        resolver.cleanup()
        print("   ✅ URLResolver cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ URLResolver test failed: {str(e)}")
        return False


def test_link_filter():
    """Test the LinkFilter class."""
    print("\n🧪 Testing LinkFilter...")
    
    try:
        from financial_report_extraction import LinkFilter
        
        # Initialize filter
        link_filter = LinkFilter()
        print("   ✅ LinkFilter initialized")
        
        # Test URL relevance filtering
        test_urls = [
            "https://example.com/investor-relations/annual-reports",
            "https://example.com/news/press-releases",
            "https://example.com/financial-statements",
            "https://example.com/careers/jobs"
        ]
        
        relevant_urls = link_filter.filter_relevant_urls(test_urls)
        print(f"   ✅ Filtered {len(test_urls)} → {len(relevant_urls)} relevant URLs")
        
        # Test URL scoring
        scores = [link_filter._calculate_url_score(url) for url in test_urls]
        print(f"   ✅ URL scores: {[f'{s:.1f}' for s in scores]}")
        
        # Test deduplication
        duplicate_urls = [
            "https://example.com/page1",
            "https://example.com/page2", 
            "https://other.com/page1",
            "https://example.com/page3"
        ]
        
        unique_urls = link_filter.deduplicate_by_domain(duplicate_urls, max_results=2)
        print(f"   ✅ Deduplicated {len(duplicate_urls)} → {len(unique_urls)} URLs")
        
        # Test domain distribution
        distribution = link_filter.get_domain_distribution(duplicate_urls)
        print(f"   ✅ Domain distribution: {distribution}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ LinkFilter test failed: {str(e)}")
        return False


def test_integration():
    """Test integration between components."""
    print("\n🧪 Testing component integration...")
    
    try:
        from financial_report_extraction import WebsiteLinkExtractor
        
        # Create realistic test data
        search_results = {
            "messages": [
                {
                    "content": """I found several sources for PLTU Suparma annual reports:
                    
                    1. San Miguel Corporation investor relations: https://www.sanmiguel.com.ph/investor-relations/
                    2. Annual reports section: https://www.smc.com.ph/investor-relations/annual-reports/
                    3. SEC filings: https://www.sec.gov/edgar/browse/?CIK=0001234567
                    
                    These contain the financial information you're looking for."""
                }
            ],
            "sources": [
                {"url": "https://grounding-api-redirect.com/redirect?url=https%3A//www.sanmiguel.com.ph/investor-relations/"},
                {"url": "https://grounding-api-redirect.com/redirect?url=https%3A//www.smc.com.ph/annual-reports/"},
                {"url": "https://example.com/news/press-release"}  # Should be filtered out
            ]
        }
        
        # Test full extraction pipeline
        extractor = WebsiteLinkExtractor()
        prioritized_urls, all_urls = extractor.extract_links_from_search_results(
            search_results, 
            max_results=3
        )
        
        print(f"   ✅ Full pipeline: {len(all_urls)} total → {len(prioritized_urls)} prioritized URLs")
        
        # Verify we got reasonable results
        if len(prioritized_urls) > 0:
            print(f"   ✅ Successfully extracted prioritized URLs")
            for i, url in enumerate(prioritized_urls, 1):
                print(f"      {i}. {url[:60]}...")
        else:
            print(f"   ⚠️  No prioritized URLs extracted (may be expected in test environment)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Integration test failed: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("Financial Report Extraction Module - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_module_imports),
        ("WebsiteLinkExtractor", test_website_link_extractor),
        ("ContentAnalyzer", test_content_analyzer),
        ("URLResolver", test_url_resolver),
        ("LinkFilter", test_link_filter),
        ("Integration", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The module is ready for integration.")
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
