#!/usr/bin/env python3
"""
Test script for the pipeline integration module
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_single_plant():
    """Test single plant processing"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if SCRAPER_API_KEY is set
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        return False
    
    print("🔧 Testing Pipeline Integration - Single Plant")
    print("=" * 50)
    
    try:
        from pipeline_integration import download_power_plant_pdfs
        
        # Test with SEIL (known to work)
        plant_name = "SEIL Energy"
        print(f"Testing with: {plant_name}")
        
        result = download_power_plant_pdfs(plant_name, download_dir="./test_pipeline_downloads")
        
        print(f"\n📊 Test Results:")
        print(f"   Plant: {result['plant_name']}")
        print(f"   URLs found: {len(result['urls_found'])}")
        print(f"   Relevant URLs: {len(result['relevant_urls'])}")
        print(f"   PDFs downloaded: {result['total_files']}")
        print(f"   Processing time: {result['processing_time']:.2f} seconds")
        print(f"   Success: {result['success']}")
        
        if result['errors']:
            print(f"   Errors: {result['errors']}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_batch_processing():
    """Test batch processing of multiple plants"""
    
    print("\n🔧 Testing Pipeline Integration - Batch Processing")
    print("=" * 50)
    
    try:
        from pipeline_integration import download_multiple_power_plants
        
        # Test with multiple plants
        plant_names = [
            "SEIL Energy",
            "PLTU Suparma"
        ]
        
        print(f"Testing batch processing with {len(plant_names)} plants")
        
        results = download_multiple_power_plants(plant_names, download_dir="./test_batch_downloads")
        
        print(f"\n📊 Batch Test Results:")
        for result in results:
            print(f"   {result['plant_name']}: {result['total_files']} PDFs, {result['processing_time']:.1f}s")
        
        successful_plants = sum(1 for r in results if r['success'])
        total_files = sum(r['total_files'] for r in results)
        
        print(f"\n   Summary: {successful_plants}/{len(plant_names)} plants successful, {total_files} total PDFs")
        
        return successful_plants > 0
        
    except Exception as e:
        print(f"❌ Batch test failed: {e}")
        return False

def test_pipeline_api():
    """Test the pipeline as an API-like function"""
    
    print("\n🔧 Testing Pipeline Integration - API Style")
    print("=" * 50)
    
    try:
        from pipeline_integration import PowerPlantPDFPipeline
        
        # Initialize pipeline
        pipeline = PowerPlantPDFPipeline(download_dir="./test_api_downloads")
        
        # Test individual steps
        plant_name = "SEIL Energy"
        
        print(f"Step 1: Searching for {plant_name}...")
        urls = pipeline.search_power_plant_urls(plant_name)
        print(f"   Found {len(urls)} URLs")
        
        if urls:
            print(f"Step 2: Analyzing URLs...")
            relevant_urls = pipeline.analyze_and_filter_urls(urls[:3], plant_name)  # Test with first 3
            print(f"   {len(relevant_urls)} relevant URLs")
            
            if relevant_urls:
                print(f"Step 3: Downloading PDFs...")
                downloaded_files = pipeline.download_pdfs_from_urls(relevant_urls[:1], plant_name)  # Test with first 1
                print(f"   Downloaded {len(downloaded_files)} files")
                
                return len(downloaded_files) > 0
        
        return False
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def save_test_results(results: dict):
    """Save test results to file"""
    try:
        with open("pipeline_test_results.json", "w") as f:
            json.dump(results, f, indent=2)
        print(f"📄 Test results saved to pipeline_test_results.json")
    except Exception as e:
        print(f"⚠️ Could not save test results: {e}")

if __name__ == "__main__":
    print("🚀 Testing Pipeline Integration Module")
    print("=" * 60)
    
    test_results = {
        "single_plant": False,
        "batch_processing": False,
        "api_style": False,
        "overall_success": False
    }
    
    # Run tests
    test_results["single_plant"] = test_single_plant()
    test_results["batch_processing"] = test_batch_processing()
    test_results["api_style"] = test_pipeline_api()
    
    # Overall success
    test_results["overall_success"] = any([
        test_results["single_plant"],
        test_results["batch_processing"], 
        test_results["api_style"]
    ])
    
    # Print final results
    print("\n🎯 FINAL TEST RESULTS")
    print("=" * 60)
    print(f"Single Plant Test: {'✅ PASS' if test_results['single_plant'] else '❌ FAIL'}")
    print(f"Batch Processing Test: {'✅ PASS' if test_results['batch_processing'] else '❌ FAIL'}")
    print(f"API Style Test: {'✅ PASS' if test_results['api_style'] else '❌ FAIL'}")
    print(f"Overall: {'✅ SUCCESS' if test_results['overall_success'] else '❌ FAILURE'}")
    
    # Save results
    save_test_results(test_results)
    
    if test_results["overall_success"]:
        print("\n🎉 Pipeline integration is working!")
        print("💡 You can now use it in your pipeline with:")
        print("   from src.pipeline_integration import download_power_plant_pdfs")
        print("   result = download_power_plant_pdfs('Plant Name')")
    else:
        print("\n❌ Pipeline integration needs debugging.")
        print("💡 Check the error messages above for troubleshooting.")
