#!/usr/bin/env python3
"""
Test script for Power Plant Annual Report Search Engine

This script tests the power plant search functionality with sample power plant names.
"""

import os
import sys
from typing import Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state, print_search_results
from agent.graph import power_plant_graph


def test_power_plant_search(plant_name: str) -> Dict[str, Any]:
    """Test the power plant search for a given plant name.
    
    Args:
        plant_name: Name of the power plant to test
        
    Returns:
        Final state from the search
    """
    print(f"\n{'='*60}")
    print(f"🧪 TESTING: {plant_name}")
    print(f"{'='*60}")
    
    try:
        # Initialize state
        initial_state = initialize_power_plant_state(plant_name)
        
        # Run the search
        final_state = power_plant_graph.invoke(initial_state)
        
        # Print results
        print_search_results(final_state)
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error testing {plant_name}: {str(e)}")
        return {}


def main():
    """Main test function."""
    print("🧪 Power Plant Annual Report Search Engine - Test Suite")
    print("=" * 60)
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running tests.")
        return
    
    # Test cases - various types of power plants
    test_cases = [
        "Palo Verde Nuclear Generating Station",
        "Three Mile Island",
        "Diablo Canyon Power Plant",
        "Vogtle Electric Generating Plant",
        "Indian Point Energy Center"
    ]
    
    print(f"🔍 Running tests for {len(test_cases)} power plants...")
    print("⚠️  Note: Each test may take 1-2 minutes to complete.\n")
    
    results = {}
    
    for plant_name in test_cases:
        try:
            result = test_power_plant_search(plant_name)
            results[plant_name] = result
            
            # Small delay between tests
            import time
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n🛑 Tests interrupted by user.")
            break
        except Exception as e:
            print(f"❌ Failed to test {plant_name}: {str(e)}")
            results[plant_name] = {"error": str(e)}
    
    # Print summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    successful_tests = 0
    for plant_name, result in results.items():
        if result and not result.get("error"):
            sources_count = len(result.get("sources_gathered", []))
            found_reports = result.get("found_annual_reports", False)
            status = "✅ SUCCESS" if sources_count > 0 else "⚠️  NO SOURCES"
            print(f"{status} - {plant_name}: {sources_count} sources found")
            if sources_count > 0:
                successful_tests += 1
        else:
            print(f"❌ FAILED - {plant_name}: {result.get('error', 'Unknown error')}")
    
    print(f"\n🎯 Results: {successful_tests}/{len(test_cases)} tests found sources")
    
    if successful_tests > 0:
        print("\n✅ Power plant search engine is working!")
        print("💡 You can now use the main interface: python src/power_plant_search.py")
    else:
        print("\n❌ No tests were successful. Please check:")
        print("   - Your GEMINI_API_KEY is valid")
        print("   - You have internet connectivity")
        print("   - The Google Search API is accessible")


if __name__ == "__main__":
    main()
