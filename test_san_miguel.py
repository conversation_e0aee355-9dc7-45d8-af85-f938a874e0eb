#!/usr/bin/env python3
"""
Test script for San Miguel Corporation PDF scraping

This script tests the PDF scraper specifically for San Miguel Corporation.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables from .env file
load_dotenv()

# Import our ScraperAPI PDF scraper
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper


def test_san_miguel_scraping():
    """Test PDF scraping specifically for San Miguel Corporation."""
    print("\n" + "="*80)
    print("🧪 TESTING SAN MIGUEL CORPORATION PDF SCRAPING")
    print("="*80)
    
    # Check for ScraperAPI key
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        print("Please set your ScraperAPI key before running this test.")
        print("Example: export SCRAPER_API_KEY='your-api-key-here'")
        return
    
    # Initialize the scraper
    scraper = ScraperAPIPDFScraper(download_dir="./test_downloads")
    
    # List of San Miguel URLs to try
    san_miguel_urls = [
        "https://www.sanmiguel.com.ph/investor-relations/annual-reports",
        "https://www.sanmiguel.com.ph/corporate/investor-relations/annual-reports",
        "https://www.sanmiguel.com.ph/corporate/investor-relations/financial-performance/annual-reports",
        "https://www.smc.com.ph/investor-relations/annual-reports/",
        "https://www.smcglobalpower.com.ph/investor-relations/annual-reports",
        "https://www.smcglobalpower.com.ph/reports/annual-reports"
    ]
    
    all_downloaded_files = []
    
    # Try each URL
    for i, url in enumerate(san_miguel_urls):
        print(f"\n🔍 Testing URL {i+1}/{len(san_miguel_urls)}: {url}")
        
        try:
            # Try to find and download PDFs from this URL
            downloaded_files = scraper.download_pdfs_from_url(url, "San_Miguel_Corporation", 3)
            
            if downloaded_files:
                print(f"✅ Found {len(downloaded_files)} PDFs on {url}")
                all_downloaded_files.extend(downloaded_files)
            else:
                print(f"❌ No PDFs found on {url}")
                
        except Exception as e:
            print(f"❌ Error testing {url}: {str(e)}")
    
    # If no PDFs found from URLs, try direct search
    if not all_downloaded_files:
        print("\n🔍 No PDFs found from URLs, trying direct search...")
        
        # Try different variations of the company name
        company_variations = [
            "San Miguel Corporation",
            "SMC Global Power Holdings",
            "San Miguel Global Power"
        ]
        
        for company in company_variations:
            print(f"\n🔍 Testing direct search for: {company}")
            
            # Try to find PDFs for the last 3 years
            current_year = 2024
            from_year = current_year - 2
            to_year = current_year
            
            try:
                direct_files = scraper.run_downloader(company, from_year, to_year)
                if direct_files:
                    print(f"✅ Found {len(direct_files)} PDFs for {company}")
                    all_downloaded_files.extend(direct_files)
                else:
                    print(f"❌ No PDFs found for {company}")
            except Exception as e:
                print(f"❌ Error during direct search for {company}: {str(e)}")
    
    # Final results
    if all_downloaded_files:
        # Remove duplicates
        unique_files = list(set(all_downloaded_files))
        print(f"\n✅ TEST PASSED: Successfully downloaded {len(unique_files)} unique PDF(s)")
        print("Downloaded files:")
        for file_path in unique_files:
            print(f"   📄 {file_path}")
    else:
        print("\n❌ TEST FAILED: No PDF files were downloaded.")


if __name__ == "__main__":
    test_san_miguel_scraping()