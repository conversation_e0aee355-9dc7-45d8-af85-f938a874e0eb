#!/usr/bin/env python3
"""
Test script to verify the SEIL PDF scraper fixes
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper

def test_seil_scraper():
    """Test the SEIL PDF scraper with the fixes"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if SCRAPER_API_KEY is set
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        print("Please set your ScraperAPI key before running this script.")
        print("Example: export SCRAPER_API_KEY='your-api-key-here'")
        return
    
    print("🔧 Testing SEIL PDF scraper fixes...")
    
    # Initialize the scraper
    scraper = ScraperAPIPDFScraper(download_dir="./annual_reports")
    
    # Test URL from the SEIL website
    test_url = "https://www.seilenergy.com/InvestorRelations/AnnualReportInv"
    plant_name = "seil"
    
    print(f"\n🔍 Testing PDF scraping from: {test_url}")
    
    try:
        # Test finding PDF links
        pdf_links = scraper.find_pdf_links(test_url)
        
        if pdf_links:
            print(f"✅ Found {len(pdf_links)} PDF links")
            
            # Try to download the first PDF
            first_pdf = pdf_links[0]
            print(f"\n⬇️  Testing download of: {first_pdf['url']}")
            
            downloaded_file = scraper.download_pdf(first_pdf['url'], plant_name)
            
            if downloaded_file:
                print(f"✅ Successfully downloaded: {downloaded_file}")
                
                # Check if file exists and has content
                if os.path.exists(downloaded_file):
                    file_size = os.path.getsize(downloaded_file)
                    print(f"📄 File size: {file_size/1024/1024:.2f} MB")
                    
                    if file_size > 1000:  # At least 1KB
                        print("✅ File appears to be valid (has content)")
                        return True
                    else:
                        print("⚠️ File is very small, might be an error page")
                        return False
                else:
                    print("❌ Downloaded file does not exist")
                    return False
            else:
                print("❌ Download failed")
                return False
        else:
            print("❌ No PDF links found")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_seil_scraper()
    if success:
        print("\n🎉 Test passed! The scraper fixes are working.")
    else:
        print("\n❌ Test failed. There may still be issues with the scraper.")
