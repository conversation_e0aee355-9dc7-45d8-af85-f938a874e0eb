#!/usr/bin/env python3
"""
Test script for the smart adaptive download system
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_smart_download():
    """Test the smart download system with PLTU Suparma URLs"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if SCRAPER_API_KEY is set
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        return
    
    print("🔧 Testing Smart Adaptive Download System...")
    
    from agent.scraper_api_pdf_scraper import Scraper<PERSON><PERSON>DFScraper
    
    # Initialize the scraper
    scraper = ScraperAPIPDFScraper(download_dir="./annual_reports")
    
    # Test URLs that are known to fail with ScraperAPI but work with direct download
    test_urls = [
        "https://www.ptsuparmatbk.com/page/download?path=file_2024_05_10_08_10_08_g96E.pdf",
        "https://www.ptsuparmatbk.com/page/download?path=file_2023_05_02_09_48_37_nBFs.pdf",
        "https://www.ptsuparmatbk.com/page/download?path=file_2022_05_02_09_48_37_test.pdf"  # This might not exist
    ]
    plant_name = "PLTU_Suparma"
    
    print(f"\n🔍 Testing smart download with {len(test_urls)} URLs")
    print("Expected behavior:")
    print("1. First URL: Try ScraperAPI → Fail → Switch to direct → Success")
    print("2. Remaining URLs: Use direct method immediately (no ScraperAPI attempts)")
    
    start_time = time.time()
    downloaded_files = []
    
    try:
        for i, url in enumerate(test_urls, 1):
            print(f"\n📄 Downloading PDF {i}/{len(test_urls)}: {os.path.basename(url)}")
            
            file_path = scraper.download_pdf_smart(url, plant_name, f"202{3+i}")
            
            if file_path:
                downloaded_files.append(file_path)
                print(f"✅ Success: {os.path.basename(file_path)}")
            else:
                print(f"❌ Failed: {os.path.basename(url)}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n📊 Results:")
        print(f"   Total time: {duration:.2f} seconds")
        print(f"   Average per PDF: {duration/len(test_urls):.2f} seconds")
        print(f"   Downloaded: {len(downloaded_files)}/{len(test_urls)} files")
        print(f"   Method failures: {scraper.method_failures}")
        print(f"   Final preferred method: {scraper.preferred_method}")
        
        if downloaded_files:
            print(f"\n📁 Downloaded files:")
            for file_path in downloaded_files:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   📄 {os.path.basename(file_path)} ({file_size/1024/1024:.2f} MB)")
                else:
                    print(f"   ❌ {file_path} (file not found)")
            return True
        else:
            print("\n❌ No files downloaded")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def compare_old_vs_new():
    """Compare old method vs new smart method timing"""
    print("\n🔍 Timing Comparison:")
    print("Old method (per PDF): ScraperAPI (3 attempts × 2s) + Direct = ~8 seconds")
    print("New method (per PDF after first): Direct only = ~2 seconds")
    print("For 6 PDFs:")
    print("  Old: 6 × 8s = ~48 seconds")
    print("  New: 8s (first) + 5 × 2s = ~18 seconds")
    print("  Time saved: ~30 seconds (62% faster!)")

if __name__ == "__main__":
    print("🚀 Testing Smart Adaptive Download System")
    print("=" * 50)
    
    compare_old_vs_new()
    
    success = test_smart_download()
    
    if success:
        print("\n🎉 Smart download test passed!")
        print("💡 The system now adapts and uses the best method for each site.")
    else:
        print("\n❌ Smart download test failed.")
        print("💡 Check the error messages above for troubleshooting.")
