#!/usr/bin/env python3
"""
Test script for the improved smart filtering in Power Plant Annual Report Search Engine

This script tests the updated filtering that focuses on content and purpose rather than just domain names.
"""

import os
import sys
from typing import Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def analyze_sources(sources):
    """Analyze the quality and relevance of returned sources."""
    print(f"\n📊 SOURCE ANALYSIS:")
    print(f"   Total sources: {len(sources)}")
    
    if len(sources) == 0:
        print("   ❌ No sources found")
        return
    
    # Check for annual report indicators
    annual_report_indicators = 0
    investor_relations_indicators = 0
    clean_urls = 0
    
    for i, source in enumerate(sources, 1):
        url = source.get('value', '').lower()
        label = source.get('label', '').lower()
        
        print(f"\n   {i}. {source.get('label', 'Unknown')}")
        print(f"      URL: {url[:80]}{'...' if len(url) > 80 else ''}")
        
        # Check for annual report indicators
        if any(keyword in url or keyword in label for keyword in ['annual', 'report']):
            annual_report_indicators += 1
            print("      ✅ Contains annual report indicators")
        
        # Check for investor relations indicators
        if any(keyword in url or keyword in label for keyword in ['investor', 'financial']):
            investor_relations_indicators += 1
            print("      ✅ Contains investor relations indicators")
        
        # Check for clean URLs
        if not any(char in url for char in ['grounding-api-redirect', '%', '?', '&']):
            clean_urls += 1
            print("      ✅ Clean, direct URL")
        else:
            print("      ⚠️  Encoded/redirect URL")
    
    print(f"\n   📈 Quality Metrics:")
    print(f"      Annual report indicators: {annual_report_indicators}/{len(sources)}")
    print(f"      Investor relations indicators: {investor_relations_indicators}/{len(sources)}")
    print(f"      Clean URLs: {clean_urls}/{len(sources)}")
    
    # Overall assessment
    if len(sources) <= 5 and annual_report_indicators > 0:
        print(f"   ✅ Good filtering - focused and relevant sources")
    elif len(sources) > 5:
        print(f"   ⚠️  Too many sources - needs better filtering")
    else:
        print(f"   ⚠️  No clear annual report sources found")


def test_smart_filtering(plant_name: str) -> Dict[str, Any]:
    """Test the smart filtering for a given plant name."""
    print(f"\n{'='*70}")
    print(f"🧠 SMART FILTERING TEST: {plant_name}")
    print(f"{'='*70}")
    
    try:
        # Initialize state
        initial_state = initialize_power_plant_state(plant_name)
        
        # Run the search
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze the sources
        sources = final_state.get("sources_gathered", [])
        analyze_sources(sources)
        
        # Check the final answer
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER ANALYSIS:")
                
                # Check if answer mentions specific URLs
                if "https://" in answer:
                    print("   ✅ Contains specific website URLs")
                else:
                    print("   ⚠️  No specific URLs mentioned")
                
                # Check if answer is focused
                if len(answer.split('\n')) < 20:
                    print("   ✅ Concise, focused answer")
                else:
                    print("   ⚠️  Answer might be too verbose")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error testing {plant_name}: {str(e)}")
        return {}


def main():
    """Main test function for smart filtering."""
    print("🧠 Power Plant Annual Report Search Engine - Smart Filtering Test")
    print("=" * 75)
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running tests.")
        return
    
    # Test cases
    test_cases = [
        "SEIL",  # Your example
        "Duke Energy",  # Large utility with clear investor relations
        "Exelon",  # Major power company
    ]
    
    print(f"🔍 Testing smart filtering for {len(test_cases)} entities...")
    print("🎯 Goal: Return 1 primary page or max 5 highly relevant pages\n")
    
    results = {}
    total_sources = 0
    high_quality_tests = 0
    
    for plant_name in test_cases:
        try:
            result = test_smart_filtering(plant_name)
            results[plant_name] = result
            
            sources = result.get("sources_gathered", [])
            sources_count = len(sources)
            total_sources += sources_count
            
            # Check quality metrics
            annual_indicators = sum(1 for s in sources 
                                  if any(keyword in s.get('value', '').lower() or keyword in s.get('label', '').lower() 
                                        for keyword in ['annual', 'report', 'investor']))
            
            if sources_count <= 5 and annual_indicators > 0:
                high_quality_tests += 1
            
            # Small delay between tests
            import time
            time.sleep(3)
            
        except KeyboardInterrupt:
            print("\n🛑 Tests interrupted by user.")
            break
        except Exception as e:
            print(f"❌ Failed to test {plant_name}: {str(e)}")
            results[plant_name] = {"error": str(e)}
    
    # Print summary
    print("\n" + "="*75)
    print("📊 SMART FILTERING TEST SUMMARY")
    print("="*75)
    
    avg_sources = total_sources / len([r for r in results.values() if not r.get("error")]) if results else 0
    
    for plant_name, result in results.items():
        if result and not result.get("error"):
            sources_count = len(result.get("sources_gathered", []))
            status = "✅ HIGH QUALITY" if sources_count <= 5 else "⚠️  TOO MANY"
            print(f"{status} - {plant_name}: {sources_count} sources")
        else:
            print(f"❌ FAILED - {plant_name}: {result.get('error', 'Unknown error')}")
    
    print(f"\n🎯 Results Summary:")
    print(f"   High quality tests: {high_quality_tests}/{len(test_cases)}")
    print(f"   Average sources per test: {avg_sources:.1f}")
    print(f"   Target: ≤ 5 relevant sources per test")
    
    if high_quality_tests >= len(test_cases) * 0.7 and avg_sources <= 5:
        print("\n✅ Smart filtering is working well!")
        print("💡 The search now returns focused, highly relevant annual report pages.")
    else:
        print("\n⚠️  Smart filtering may need further refinement.")
        print("   Consider adjusting the priority scoring or filtering logic.")


if __name__ == "__main__":
    main()
