#!/usr/bin/env python3
"""
Test script to debug why sources are being filtered out completely
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_source_debug():
    """Debug the source filtering for PLTU Suparma."""
    print("🐛 Debugging Source Filtering for PLTU Suparma")
    print("=" * 55)
    print("🎯 Goal: Understand why sources are being filtered out")
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for PLTU Suparma
        initial_state = initialize_power_plant_state("PLTU Suparma")
        
        print(f"\n🔍 Initial state plant_name: {initial_state.get('plant_name', 'NOT SET')}")
        
        # Run the search
        print("\n🔍 Running search for PLTU Suparma...")
        print("   (Watch for DEBUG messages in the output)")
        
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze results
        sources = final_state.get("sources_gathered", [])
        web_results = final_state.get("web_research_result", [])
        
        print(f"\n📊 DEBUG RESULTS:")
        print(f"   Sources in final state: {len(sources)}")
        print(f"   Web research results: {len(web_results)}")
        
        # Check if web research found anything
        if web_results:
            print(f"\n📄 WEB RESEARCH CONTENT:")
            for i, result in enumerate(web_results, 1):
                print(f"   Result {i}: {len(result)} characters")
                if "annual report" in result.lower():
                    print(f"      ✅ Contains 'annual report'")
                if "pltu" in result.lower() or "suparma" in result.lower():
                    print(f"      ✅ Contains PLTU/Suparma references")
        
        # Check sources in detail
        if sources:
            print(f"\n🔍 PRESERVED SOURCES:")
            for i, source in enumerate(sources, 1):
                label = source.get('label', 'Unknown')
                url = source.get('value', '')
                short_url = source.get('short_url', '')
                
                print(f"\n   {i}. Label: {label}")
                print(f"      Value: {url[:80]}{'...' if len(url) > 80 else ''}")
                print(f"      Short URL: {short_url}")
        else:
            print(f"\n❌ NO SOURCES PRESERVED")
            print(f"   This means the filtering is too aggressive")
        
        # Check final answer
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER ANALYSIS:")
                print(f"   Answer length: {len(answer)} characters")
                
                # Check for URLs in the answer
                import re
                urls_in_answer = re.findall(r'https?://[^\s\)]+', answer)
                print(f"   URLs found in answer: {len(urls_in_answer)}")
                
                for i, url in enumerate(urls_in_answer, 1):
                    print(f"      {i}. {url[:80]}{'...' if len(url) > 80 else ''}")
                
                # This is the key issue: URLs are in the answer but not in sources
                if urls_in_answer and not sources:
                    print(f"\n🚨 ISSUE IDENTIFIED:")
                    print(f"   ❌ URLs found in answer but NO sources preserved")
                    print(f"   💡 This means filtering is removing sources that contain the URLs")
                    print(f"   💡 The AI found the info but sources were filtered out")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if not sources and web_results:
            print(f"   1. Web research is working (found {len(web_results)} results)")
            print(f"   2. Source filtering is too aggressive")
            print(f"   3. Need to preserve sources that contain annual report info")
        elif not sources and not web_results:
            print(f"   1. Web research itself may be failing")
            print(f"   2. Check search query generation")
        elif sources:
            print(f"   1. Sources are being preserved correctly")
            print(f"   2. Check why they're not showing in the UI")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


if __name__ == "__main__":
    test_source_debug()
