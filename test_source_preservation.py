#!/usr/bin/env python3
"""
Test script to verify that relevant sources are preserved and not over-filtered
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_source_preservation():
    """Test that relevant sources are preserved for PLTU Suparma."""
    print("🔧 Testing Source Preservation for PLTU Suparma")
    print("=" * 50)
    print("🎯 Goal: Ensure relevant sources are NOT over-filtered")
    print("🎯 Expected: At least 1 source should be preserved")
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for PLTU Suparma
        initial_state = initialize_power_plant_state("PLTU Suparma")
        
        # Run the search
        print("\n🔍 Running search for PLTU Suparma...")
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze results
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 SOURCE PRESERVATION RESULTS:")
        print(f"   Sources found: {len(sources)}")
        
        if len(sources) == 0:
            print(f"   ❌ FAILED! No sources preserved - over-filtering detected")
        elif len(sources) == 1:
            print(f"   🎯 PERFECT! Exactly 1 source preserved")
        elif len(sources) <= 3:
            print(f"   ✅ GOOD! {len(sources)} sources preserved")
        else:
            print(f"   ⚠️  Many sources: {len(sources)} (may need more filtering)")
        
        # Check final answer
        has_useful_info = False
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER ANALYSIS:")
                
                # Check if answer has useful information
                if any(keyword in answer.lower() for keyword in ['annual report', 'pltu', 'suparma']):
                    has_useful_info = True
                    print("   ✅ Contains useful information about PLTU Suparma")
                else:
                    print("   ❌ No useful information found")
                
                # Check for URLs
                if "http" in answer:
                    print("   ✅ Contains website URLs")
                else:
                    print("   ⚠️  No website URLs found")
        
        # Analyze each source if any
        if sources:
            print(f"\n🔍 PRESERVED SOURCES:")
            for i, source in enumerate(sources, 1):
                label = source.get('label', 'Unknown')
                url = source.get('value', '')
                
                print(f"\n   {i}. Label: {label}")
                print(f"      URL: {url[:60]}{'...' if len(url) > 60 else ''}")
                
                # Check relevance
                if any(keyword in label.lower() for keyword in ['ptsuparmatbk', 'suparma', 'pltu']):
                    print(f"      ✅ Relevant to PLTU Suparma")
                else:
                    print(f"      ⚠️  Relevance unclear")
        
        # Overall assessment
        print(f"\n🏆 OVERALL ASSESSMENT:")
        
        if len(sources) > 0 and has_useful_info:
            print("   🏆 EXCELLENT! Sources preserved AND useful info found")
            print("   💡 System is working correctly")
        elif len(sources) > 0:
            print("   ✅ GOOD! Sources preserved but check info quality")
        elif has_useful_info:
            print("   ⚠️  PARTIAL! Info found but sources over-filtered")
            print("   💡 Need to preserve more sources")
        else:
            print("   ❌ FAILED! No sources AND no useful info")
            print("   💡 Filtering is too aggressive")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


if __name__ == "__main__":
    test_source_preservation()
