#!/usr/bin/env python3
"""
Test script for ultra-aggressive filtering - should return only 1 source
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_ultra_filtering():
    """Test the ultra-aggressive filtering for SEIL."""
    print("⚡ Testing Ultra-Aggressive Filtering for SEIL")
    print("=" * 55)
    print("🎯 Target: Return ONLY 1 source (the best one)")
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for SEIL
        initial_state = initialize_power_plant_state("SEIL")
        
        # Run the search
        print("\n🔍 Running search with ultra-aggressive filtering...")
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze results
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 ULTRA-FILTERING RESULTS:")
        print(f"   Sources returned: {len(sources)}")
        print(f"   Target: 1 source")
        
        if len(sources) == 1:
            print(f"   🎯 PERFECT! Returned exactly 1 source")
        elif len(sources) <= 3:
            print(f"   ✅ GOOD! Returned {len(sources)} sources (close to target)")
        else:
            print(f"   ❌ FAILED! Returned {len(sources)} sources (way too many)")
        
        # Analyze source quality
        print(f"\n🔍 SOURCE QUALITY ANALYSIS:")
        for i, source in enumerate(sources, 1):
            label = source.get('label', 'Unknown')
            url = source.get('value', '')
            
            print(f"   {i}. {label}")
            
            # Check if it's a third-party site
            third_party_sites = ['tracxn', 'zaubacorp', 'careratings', 'pitchbook', 
                               'crisilratings', 'icra', 'crisil']
            
            if any(site in label.lower() or site in url.lower() for site in third_party_sites):
                print(f"      ❌ THIRD-PARTY SITE (should be filtered out!)")
            else:
                print(f"      ✅ Official company source")
            
            # Check for SEIL Energy specifically
            if 'seilenergy' in label.lower():
                print(f"      ✅ SEIL Energy official source")
            else:
                print(f"      ⚠️  Not SEIL Energy official source")
        
        # Check final answer
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER ANALYSIS:")
                
                # Check for the correct URL
                if "seilenergy.com/InvestorRelations/AnnualReportInv" in answer:
                    print("   🎯 PERFECT! Contains the exact correct URL")
                elif "seilenergy.com" in answer:
                    print("   ✅ GOOD! Contains SEIL Energy website")
                else:
                    print("   ❌ MISSING! No SEIL Energy website found")
                
                # Check if answer mentions only one URL
                url_count = answer.lower().count('http')
                if url_count == 1:
                    print("   ✅ FOCUSED! Mentions only 1 URL")
                elif url_count <= 3:
                    print("   ✅ GOOD! Mentions few URLs")
                else:
                    print("   ⚠️  TOO MANY URLs mentioned")
        
        # Overall assessment
        print(f"\n🏆 FINAL ASSESSMENT:")
        
        # Count third-party sources
        third_party_count = sum(1 for s in sources 
                              if any(site in s.get('label', '').lower() 
                                   for site in ['careratings', 'crisilratings', 'icra', 'tracxn']))
        
        seil_count = sum(1 for s in sources if 'seilenergy' in s.get('label', '').lower())
        
        if len(sources) == 1 and seil_count == 1 and third_party_count == 0:
            print("   🏆 EXCELLENT! Perfect ultra-aggressive filtering")
            print("   💡 Ready for production - no more infinite loops!")
        elif len(sources) <= 3 and third_party_count == 0:
            print("   ✅ VERY GOOD! Clean filtering, minimal sources")
            print("   💡 Should work well for PDF scraping")
        elif third_party_count == 0:
            print("   ✅ GOOD! No third-party sources, but could be more focused")
        else:
            print("   ❌ NEEDS MORE WORK! Still has third-party sources")
            print(f"   📊 Third-party sources: {third_party_count}")
            print(f"   📊 SEIL sources: {seil_count}")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return {}


if __name__ == "__main__":
    test_ultra_filtering()
